{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "bun run --hot --env-file=.env src/index.ts", "start": "bun run dist/index.js"}, "dependencies": {"@hono/trpc-server": "^0.4.0", "@trpc/client": "^11.4.2", "@trpc/server": "^11.4.2", "axios": "^1.11.0", "dotenv": "^17.2.1", "hono": "^4.8.2", "mongoose": "^8.14.0", "zod": "^4.0.2"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.5.1", "@types/bun": "^1.2.6", "prettier": "^3.6.2", "tsdown": "^0.12.9", "typescript": "^5.8.2"}}