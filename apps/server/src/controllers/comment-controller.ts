/**
 * Controller layer for comment HTTP operations
 * Handles comment-related request/response logic and validation
 */

import { TRPCError } from "@trpc/server"

import type { AddComment, DeleteComment, GetComments } from "@/schemas/meeting"
import { CommentService, CommentServiceError } from "@/services/comment-service"
import type { ICommentService } from "@/services/comment-service"
import { MeetingService, MeetingServiceError } from "@/services/meeting-service"
import type { IMeetingService } from "@/services/meeting-service"

export class CommentControllerError extends Error {
  constructor(
    message: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = "CommentControllerError"
  }
}

export interface CommentControllerResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  count?: number
}

export interface ICommentController {
  addCommentById(input: { meetId: number; comment: string; userId: string }): Promise<CommentControllerResponse>
  getCommentsById(input: { meetId: number }): Promise<CommentControllerResponse>
  deleteComment(input: DeleteComment & { userId?: string }): Promise<CommentControllerResponse>
  getCommentsByUserId(input: { userId: string }): Promise<CommentControllerResponse>
}

export class CommentController implements ICommentController {
  private meetingService: IMeetingService
  private commentService: ICommentService

  constructor(meetingService: IMeetingService, commentService: ICommentService) {
    this.meetingService = meetingService
    this.commentService = commentService
  }

  /**
   * Add a comment to a meeting by ID (primary method)
   */
  async addCommentById(input: { meetId: number; comment: string; userId: string }): Promise<CommentControllerResponse> {
    try {
      const comment = await this.commentService.addCommentById(input.meetId, input.comment, input.userId)

      return {
        success: true,
        data: comment.toSummary(),
        message: "Comment added successfully",
      }
    } catch (error) {
      if (error instanceof MeetingServiceError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
          cause: error,
        })
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to add comment",
      })
    }
  }

  /**
   * Get comments for a meeting by ID (primary method)
   */
  async getCommentsById(input: { meetId: number }): Promise<CommentControllerResponse> {
    try {
      const comments = await this.commentService.getCommentsById(input.meetId)

      return {
        success: true,
        data: comments.map((comment) => comment.toSummary()),
        count: comments.length,
        message: "Comments retrieved successfully",
      }
    } catch (error) {
      if (error instanceof MeetingServiceError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
          cause: error,
        })
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get comments",
      })
    }
  }

  /**
   * Delete a comment
   */
  async deleteComment(input: DeleteComment & { userId?: string }): Promise<CommentControllerResponse> {
    try {
      const deleted = await this.commentService.deleteComment(input.commentId, input.userId)

      if (!deleted) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Comment not found or you don't have permission to delete it",
        })
      }

      return {
        success: true,
        message: "Comment deleted successfully",
      }
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error
      }

      if (error instanceof MeetingServiceError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
          cause: error,
        })
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete comment",
      })
    }
  }

  /**
   * Get comments by user ID
   */
  async getCommentsByUserId(input: { userId: string }): Promise<CommentControllerResponse> {
    try {
      const comments = await this.commentService.getCommentsByUser(input.userId)

      return {
        success: true,
        data: comments.map((comment) => comment.toSummary()),
        count: comments.length,
        message: "User comments retrieved successfully",
      }
    } catch (error) {
      if (error instanceof MeetingServiceError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
          cause: error,
        })
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get user comments",
      })
    }
  }

  /**
   * Validate comment input
   */
  private validateCommentInput(comment: string): void {
    if (!comment || comment.trim().length === 0) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Comment cannot be empty",
      })
    }

    if (comment.length > 1000) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Comment cannot exceed 1000 characters",
      })
    }
  }

  /**
   * Validate user ID input
   */
  private validateUserIdInput(userId: string): void {
    if (!userId || userId.trim().length === 0) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "User ID is required",
      })
    }
  }

  /**
   * Validate meeting ID input
   */
  private validateMeetIdInput(meetId: number): void {
    if (!meetId || meetId <= 0) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Valid meeting ID is required",
      })
    }
  }

  /**
   * Add comment with validation by ID
   */
  async addCommentByIdWithValidation(input: {
    meetId: number
    comment: string
    userId: string
  }): Promise<CommentControllerResponse> {
    this.validateMeetIdInput(input.meetId)
    this.validateCommentInput(input.comment)
    this.validateUserIdInput(input.userId)

    return this.addCommentById(input)
  }

  /**
   * Get comments with validation by ID
   */
  async getCommentsByIdWithValidation(input: { meetId: number }): Promise<CommentControllerResponse> {
    this.validateMeetIdInput(input.meetId)

    return this.getCommentsById(input)
  }
}
