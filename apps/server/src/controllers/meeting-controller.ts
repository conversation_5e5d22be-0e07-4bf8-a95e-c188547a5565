/**
 * Controller layer for meeting HTTP operations
 * Handles request/response logic and validation, delegates business logic to services
 */

import { TRPCError } from "@trpc/server"
import type { z } from "zod"

import type { PartnerType } from "@/types/meeting"
import type { FetchMeetingDataInput } from "@/schemas/cvm-api"
import type { GetMeetingById, GetMeetingsQuery } from "@/schemas/meeting"
import { MeetingService, MeetingServiceError } from "@/services/meeting-service"
import type { IMeetingService } from "@/services/meeting-service"

export class MeetingControllerError extends Error {
  constructor(
    message: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = "MeetingControllerError"
  }
}

export interface MeetingControllerResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  count?: number
  total?: number
  page?: number
  limit?: number
  totalPages?: number
}

export interface MeetingController {
  fetchAndProcessMeetingData(input: FetchMeetingDataInput): Promise<MeetingControllerResponse>
  fetchAndSaveMeetingData(input: FetchMeetingDataInput): Promise<MeetingControllerResponse>
  getMeetings(input: GetMeetingsQuery): Promise<MeetingControllerResponse>
  getMeetingById(meetId: number): Promise<MeetingControllerResponse>
}

export class MeetingController implements MeetingController {
  private meetingService: IMeetingService

  constructor(meetingService: IMeetingService) {
    this.meetingService = meetingService
  }

  /**
   * Fetch and process meeting data from CVM API
   */
  async fetchAndProcessMeetingData(input: FetchMeetingDataInput): Promise<MeetingControllerResponse> {
    try {
      const processedData = await this.meetingService.fetchAndProcessMeetingData(input)

      return {
        success: true,
        data: processedData,
        count: processedData.length,
        message: `Successfully processed ${processedData.length} meetings`,
      }
    } catch (error) {
      if (error instanceof MeetingServiceError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
          cause: error,
        })
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch and process meeting data",
      })
    }
  }

  /**
   * Fetch and save meeting data from CVM API
   */
  async fetchAndSaveMeetingData(input: FetchMeetingDataInput): Promise<MeetingControllerResponse> {
    try {
      const savedMeetings = await this.meetingService.fetchAndSaveMeetingData(input)

      return {
        success: true,
        data: savedMeetings.map((meeting) => meeting.toSummary()),
        count: savedMeetings.length,
        message: `Successfully saved ${savedMeetings.length} meetings`,
      }
    } catch (error) {
      if (error instanceof MeetingServiceError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
          cause: error,
        })
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch and save meeting data",
      })
    }
  }

  /**
   * Get meetings with pagination and filtering
   */
  async getMeetings(input: GetMeetingsQuery): Promise<MeetingControllerResponse> {
    try {
      const result = await this.meetingService.getMeetings({
        page: input.page,
        limit: input.limit,
        startDate: input.startDate,
        endDate: input.endDate,
        partnerType: input.partnerType,
        meetStatus: input.meetStatus,
        companyName: input.companyName,
      })

      return {
        success: true,
        data: result.meetings.map((meeting) => meeting.toSummary()),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
      }
    } catch (error) {
      if (error instanceof MeetingServiceError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
          cause: error,
        })
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get meetings",
      })
    }
  }

  /**
   * Get meeting by ID (primary method)
   */
  async getMeetingById(meetId: number): Promise<MeetingControllerResponse> {
    try {
      const meeting = await this.meetingService.getMeetingById(meetId)

      if (!meeting) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `Meeting with ID ${meetId} not found`,
        })
      }

      return {
        success: true,
        data: meeting.toSummary(),
        message: "Meeting retrieved successfully",
      }
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error
      }

      if (error instanceof MeetingServiceError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
          cause: error,
        })
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get meeting",
      })
    }
  }
}
