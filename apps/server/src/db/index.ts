import mongoose from "mongoose"

await mongoose.connect(process.env.DATABASE_URL || "").catch((error) => {
  console.log("Error connecting to database:", error)
})

// Add connection event listeners
mongoose.connection.on("connected", () => {
  console.log("✅ MongoDB connected successfully")
})

mongoose.connection.on("error", (error) => {
  console.error("❌ MongoDB connection error:", error)
})

mongoose.connection.on("disconnected", () => {
  console.log("⚠️ MongoDB disconnected")
})

const client = mongoose.connection.getClient().db("platform-meeting")

console.log("✅ MongoDB client created successfully")

export { client }
