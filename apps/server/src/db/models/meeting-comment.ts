/**
 * Mongoose model for meeting comments
 */

import mongoose, { Document, Schema } from "mongoose"

import type { IMeetingComment } from "@/types/meeting"

// Interface for the document
export interface IMeetingCommentDocument extends IMeetingComment, Document {
  createdAt: Date
  updatedAt: Date

  toSummary(): IMeetingComment
}

// Schema definition
const meetingCommentSchema = new Schema<IMeetingCommentDocument>(
  {
    meetId: { type: Number, required: true, index: true }, // Primary key for data operations
    userId: { type: String, required: true, index: true },
    comment: { type: String, required: true, maxlength: 1000 },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt
    collection: "meeting_comments",
  }
)

// Indexes for performance
meetingCommentSchema.index({ meetId: 1, createdAt: -1 }) // Primary index for getting comments by meeting
meetingCommentSchema.index({ userId: 1, createdAt: -1 }) // For getting comments by user

// Static methods
meetingCommentSchema.statics.findByMeetingId = function (meetId: number) {
  return this.find({ meetId }).sort({ createdAt: -1 }).exec()
}

meetingCommentSchema.statics.findByUserId = function (userId: string) {
  return this.find({ userId }).sort({ createdAt: -1 }).exec()
}

meetingCommentSchema.statics.deleteByMeetingId = function (meetId: number) {
  return this.deleteMany({ meetId }).exec()
}

// Instance methods
meetingCommentSchema.methods.toSummary = function () {
  return {
    id: this._id,
    meetId: this.meetId,
    userId: this.userId,
    comment: this.comment,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt,
  }
}

// Pre-save middleware for validation
meetingCommentSchema.pre("save", function (next) {
  // Trim whitespace from comment
  if (this.comment) {
    this.comment = this.comment.trim()
  }

  // Validate comment length
  if (!this.comment || this.comment.length === 0) {
    return next(new Error("Comment cannot be empty"))
  }

  if (this.comment.length > 1000) {
    return next(new Error("Comment cannot exceed 1000 characters"))
  }

  next()
})

// Export the model
export const MeetingComment = mongoose.model<IMeetingCommentDocument>("MeetingComment", meetingCommentSchema)
