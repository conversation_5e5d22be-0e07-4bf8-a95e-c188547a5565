/**
 * Mongoose model for meeting data
 */

import mongoose, { Document, Schema } from "mongoose"

import { PartnerType } from "@/types/meeting"
import type { ProcessedMeetingData } from "@/types/meeting"

// Interface for the document
export interface IMeetingDocument extends ProcessedMeetingData, Document {
  createdAt: Date
  updatedAt: Date

  formattedMeetDate: string
  hasCLCAppointment: boolean
  hasPLCAppointment: boolean
  toSummary(): IMeetingDocument
}

// Schema definition
const meetingSchema = new Schema<IMeetingDocument>(
  {
    // Basic Meeting Information
    meetDate: { type: String },
    meetTime: { type: String },
    meetId: { type: Number, unique: true, index: true }, // Primary key for data operations
    meetErpCode: { type: String, index: true }, // Display field, not unique
    companyName: { type: String, index: true },
    block: { type: String },
    sumGrade: { type: String },
    meetingChannel: { type: String },
    meetStatus: { type: String, index: true },
    meetingCreatedAt: { type: String },

    // Customer Information
    customerName: { type: String, index: true },
    customerPosition: { type: String },
    customerPhone: { type: String },
    customerEmail: { type: String },
    customerConference: { type: String },
    customerOtherDesc: { type: String },
    typeOfBusiness: { type: String },
    customerCLC: { type: String, default: null },
    customerVisit: { type: String },
    customerOnsiteOther: { type: String },
    customerPLC: { type: String, default: null },

    // Address Information
    customerAddressCompany: { type: String },
    subDistrict: { type: String },
    district: { type: String },
    province: { type: String, index: true },
    postalCode: { type: String },

    // Sales Information
    saleName: { type: String, index: true },
    blockTeamSale: { type: String },
    presaleCLCName: { type: String },
    presaleVisitName: { type: String },
    presalePLCName: { type: String },
    invoiceHeader: { type: String },

    // Additional CVM Fields
    taxId: { type: String, default: null },
    saleMOM: { type: String, default: null },
    presaleMOM: { type: String, default: null },
    service: { type: String, default: null },
    statusService: { type: String, default: null },
    partnerERP: { type: String, default: null },
    serviceERP: { type: String, default: null },
    partnerCRM: { type: String, default: null },
    serviceCRM: { type: String, default: null },
    partnerHRM: { type: String, default: null },
    serviceHRM: { type: String, default: null },
    partnerSupplyChain: { type: String, default: null },
    serviceSupplyChain: { type: String, default: null },
    partnerWorkflow: { type: String, default: null },
    serviceWorkflow: { type: String, default: null },
    partnerEcommerceWeb: { type: String, default: null },
    serviceEcommerceWeb: { type: String, default: null },

    // Computed Fields
    partnerType: {
      type: String,
      enum: Object.values(PartnerType),
      index: true,
    },
    privilege: { type: String },
    presalesPrivilegeName: { type: String },

    // Metadata
    processedAt: { type: Date, default: Date.now },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt
    collection: "meetings",
  }
)

// Indexes for performance
meetingSchema.index({ meetDate: -1 }) // For date-based queries
meetingSchema.index({ meetDate: -1, partnerType: 1 }) // Compound index for common queries
meetingSchema.index({ companyName: "text", customerName: "text" }) // Text search
meetingSchema.index({ processedAt: -1 }) // For recent data queries

// Virtual for formatted meet date
meetingSchema.virtual("formattedMeetDate").get(function () {
  try {
    return new Date(this.meetDate).toLocaleDateString()
  } catch {
    return this.meetDate
  }
})

// Virtual for CLC appointment status
meetingSchema.virtual("hasCLCAppointment").get(function () {
  return this.customerCLC !== null && this.customerCLC !== ""
})

// Virtual for PLC appointment status
meetingSchema.virtual("hasPLCAppointment").get(function () {
  return this.customerPLC !== null && this.customerPLC !== ""
})

meetingSchema.statics.getStatistics = async function () {
  const pipeline = [
    {
      $group: {
        _id: null,
        totalMeetings: { $sum: 1 },
        partnerTypes: { $push: "$partnerType" },
        privileges: { $push: "$privilege" },
        statuses: { $push: "$meetStatus" },
      },
    },
    {
      $project: {
        _id: 0,
        totalMeetings: 1,
        partnerTypeBreakdown: {
          $arrayToObject: {
            $map: {
              input: { $setUnion: ["$partnerTypes"] },
              as: "type",
              in: {
                k: "$$type",
                v: {
                  $size: {
                    $filter: {
                      input: "$partnerTypes",
                      cond: { $eq: ["$$this", "$$type"] },
                    },
                  },
                },
              },
            },
          },
        },
        privilegeBreakdown: {
          $arrayToObject: {
            $map: {
              input: { $setUnion: ["$privileges"] },
              as: "privilege",
              in: {
                k: "$$privilege",
                v: {
                  $size: {
                    $filter: {
                      input: "$privileges",
                      cond: { $eq: ["$$this", "$$privilege"] },
                    },
                  },
                },
              },
            },
          },
        },
        statusBreakdown: {
          $arrayToObject: {
            $map: {
              input: { $setUnion: ["$statuses"] },
              as: "status",
              in: {
                k: "$$status",
                v: {
                  $size: {
                    $filter: {
                      input: "$statuses",
                      cond: { $eq: ["$$this", "$$status"] },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  ]

  const result = await this.aggregate(pipeline)
  return (
    result[0] || {
      totalMeetings: 0,
      partnerTypeBreakdown: {},
      privilegeBreakdown: {},
      statusBreakdown: {},
    }
  )
}

// Instance methods
meetingSchema.methods.toSummary = function () {
  return {
    id: this._id,
    ...this.toObject(),
  }
}

// Pre-save middleware
meetingSchema.pre("save", function (next) {
  if (this.isNew) {
    this.processedAt = new Date()
  }
  next()
})

// Export the model
export const Meeting = mongoose.model<IMeetingDocument>("Meeting", meetingSchema)
