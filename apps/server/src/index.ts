import { trpcServer } from "@hono/trpc-server"
import { <PERSON><PERSON> } from "hono"
import { cors } from "hono/cors"
import { logger } from "hono/logger"

import { getConfig, validateConfig } from "./lib/config"
import { createContext } from "./lib/context"
import { appRouter } from "./routers/index"

import "./db"

// Validate configuration on startup
validateConfig()
const config = getConfig()

const app = new Hono()

app.use(logger())
app.use(
  "/*",
  cors({
    origin: config.corsOrigin,
    allowMethods: ["GET", "POST", "OPTIONS"],
  })
)

app.use(
  "/trpc/*",
  trpcServer({
    router: appRouter,
    createContext: (_opts, context) => {
      return createContext({ context })
    },
  })
)

app.get("/", (c) => {
  return c.text("OK")
})

export default app
