/**
 * Configuration validation and management
 */
import { createConfigError } from "./error-handler"

export interface AppConfig {
  // Database
  databaseUrl: string

  // CORS
  corsOrigin: string

  // CVM API
  cvmApiBaseUrl: string
  cvmApiClientId: string
  cvmApiClientSecret: string

  // Server
  port: number
  nodeEnv: string
}

/**
 * Validates and returns application configuration
 */
export function getConfig(): AppConfig {
  const requiredEnvVars = ["DATABASE_URL", "CVM_API_BASE_URL", "CVM_API_CLIENT_ID", "CVM_API_CLIENT_SECRET"]

  const missingVars = requiredEnvVars.filter((varName) => !process.env[varName])

  if (missingVars.length > 0) {
    throw createConfigError(`Missing required environment variables: ${missingVars.join(", ")}`, missingVars)
  }

  return {
    databaseUrl: process.env.DATABASE_URL!,
    corsOrigin: process.env.CORS_ORIGIN || "http://localhost:3001",
    cvmApiBaseUrl: process.env.CVM_API_BASE_URL!,
    cvmApiClientId: process.env.CVM_API_CLIENT_ID!,
    cvmApiClientSecret: process.env.CVM_API_CLIENT_SECRET!,
    port: parseInt(process.env.PORT || "3000", 10),
    nodeEnv: process.env.NODE_ENV || "development",
  }
}

/**
 * Validates configuration on startup
 */
export function validateConfig(): void {
  try {
    const config = getConfig()
    console.log("✅ Configuration validated successfully")
    console.log(`   - Environment: ${config.nodeEnv}`)
    console.log(`   - Port: ${config.port}`)
    console.log(`   - CVM API: ${config.cvmApiBaseUrl}`)
    console.log(`   - Database: ${config.databaseUrl ? "Connected" : "Not configured"}`)
  } catch (error) {
    console.error("❌ Configuration validation failed:", error)
    process.exit(1)
  }
}

/**
 * Check if running in development mode
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === "development"
}

/**
 * Check if running in production mode
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === "production"
}

/**
 * Check if running in test mode
 */
export function isTest(): boolean {
  return process.env.NODE_ENV === "test"
}
