/**
 * Centralized error handling utilities
 */

export enum ErrorCode {
  // API Errors
  CVM_API_ERROR = "CVM_API_ERROR",
  HTTP_CLIENT_ERROR = "HTTP_CLIENT_ERROR",

  // Service Errors
  MEETING_SERVICE_ERROR = "MEETING_SERVICE_ERROR",
  DATA_PROCESSING_ERROR = "DATA_PROCESSING_ERROR",

  // Database Errors
  DATABASE_ERROR = "DATABASE_ERROR",
  VALIDATION_ERROR = "VALIDATION_ERROR",

  // Configuration Errors
  CONFIG_ERROR = "CONFIG_ERROR",

  // Unknown Errors
  UNKNOWN_ERROR = "UNKNOWN_ERROR",
}

export interface ErrorDetails {
  code: ErrorCode
  message: string
  originalError?: Error
  context?: Record<string, any>
  timestamp: Date
}

export class AppError extends Error {
  public readonly code: ErrorCode
  public readonly originalError?: Error
  public readonly context?: Record<string, any>
  public readonly timestamp: Date

  constructor(details: Omit<ErrorDetails, "timestamp">) {
    super(details.message)
    this.name = "AppError"
    this.code = details.code
    this.originalError = details.originalError
    this.context = details.context
    this.timestamp = new Date()
  }

  toJSON(): ErrorDetails {
    return {
      code: this.code,
      message: this.message,
      originalError: this.originalError,
      context: this.context,
      timestamp: this.timestamp,
    }
  }
}

/**
 * Error logger utility
 */
export class ErrorLogger {
  static log(error: Error | AppError, context?: Record<string, any>): void {
    const errorInfo = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      context,
    }

    if (error instanceof AppError) {
      errorInfo.context = { ...errorInfo.context, ...error.context }
    }

    // In production, you might want to send this to a logging service
    console.error("Application Error:", JSON.stringify(errorInfo, null, 2))
  }

  static logAndThrow(code: ErrorCode, message: string, originalError?: Error, context?: Record<string, any>): never {
    const appError = new AppError({
      code,
      message,
      originalError,
      context,
    })

    this.log(appError)
    throw appError
  }
}

/**
 * Error handler for async functions
 */
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  errorCode: ErrorCode,
  contextFn?: (...args: T) => Record<string, any>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args)
    } catch (error) {
      const context = contextFn ? contextFn(...args) : undefined

      if (error instanceof AppError) {
        ErrorLogger.log(error, context)
        throw error
      }

      ErrorLogger.logAndThrow(
        errorCode,
        error instanceof Error ? error.message : "Unknown error occurred",
        error instanceof Error ? error : undefined,
        context
      )
    }
  }
}

/**
 * Validation error helper
 */
export function createValidationError(message: string, field?: string, value?: any): AppError {
  return new AppError({
    code: ErrorCode.VALIDATION_ERROR,
    message,
    context: { field, value },
  })
}

/**
 * Configuration error helper
 */
export function createConfigError(message: string, missingConfig?: string[]): AppError {
  return new AppError({
    code: ErrorCode.CONFIG_ERROR,
    message,
    context: { missingConfig },
  })
}

/**
 * Database error helper
 */
export function createDatabaseError(message: string, operation?: string, originalError?: Error): AppError {
  return new AppError({
    code: ErrorCode.DATABASE_ERROR,
    message,
    originalError,
    context: { operation },
  })
}

/**
 * Retry utility with exponential backoff
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: {
    maxRetries?: number
    baseDelay?: number
    maxDelay?: number
    backoffFactor?: number
  } = {}
): Promise<T> {
  const { maxRetries = 3, baseDelay = 1000, maxDelay = 10000, backoffFactor = 2 } = options

  let lastError: Error

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error("Unknown error")

      if (attempt === maxRetries) {
        break
      }

      const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay)
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }

  throw lastError!
}
