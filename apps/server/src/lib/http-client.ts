/**
 * HTTP client utility for making API requests with proper error handling
 * Refactored to use Axios for better reliability and maintainability
 */

import axios from "axios"
import type { AxiosError, AxiosInstance, AxiosRequestConfig } from "axios"

export interface HttpClientOptions {
  baseURL?: string
  timeout?: number
  headers?: Record<string, string>
}

export interface RequestOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE"
  headers?: Record<string, string>
  body?: any
  timeout?: number
}

export class HttpClientError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: any
  ) {
    super(message)
    this.name = "HttpClientError"
  }
}

export class HttpClient {
  private axiosInstance: AxiosInstance

  constructor(options: HttpClientOptions = {}) {
    // Create Axios instance with default configuration
    this.axiosInstance = axios.create({
      baseURL: options.baseURL || "",
      timeout: options.timeout || 30000,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    })

    // Add request interceptor for debugging (optional)
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // Log request details in development
        if (process.env.NODE_ENV === "development") {
          console.debug(`HTTP ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`)
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Add response interceptor for consistent error handling
    this.axiosInstance.interceptors.response.use(
      (response) => {
        // Log response details in development
        if (process.env.NODE_ENV === "development") {
          console.debug(`HTTP ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`)
        }
        return response
      },
      (error: AxiosError) => {
        if (error.response) {
          // Server responded with error status
          const errorMessage = `HTTP ${error.response.status}: ${error.response.statusText}`
          throw new HttpClientError(errorMessage, error.response.status, error.response.data)
        } else if (error.request) {
          // Request was made but no response received
          if (error.code === "ECONNABORTED" || error.code === "ETIMEDOUT") {
            throw new HttpClientError("Request timeout")
          }
          if (error.code === "ECONNREFUSED") {
            throw new HttpClientError("Connection refused - server may be down")
          }
          if (error.code === "ENOTFOUND") {
            throw new HttpClientError("Network error - host not found")
          }
          throw new HttpClientError(`Request failed: ${error.message}`)
        } else {
          // Something else happened
          throw new HttpClientError(`Request failed: ${error.message}`)
        }
      }
    )
  }

  async request<T>(url: string, options: RequestOptions = {}): Promise<T> {
    try {
      // Prepare Axios request config
      const config: AxiosRequestConfig = {
        url,
        method: options.method || "GET",
        headers: options.headers,
        timeout: options.timeout,
      }

      // Add data/body for POST, PUT, etc.
      if (options.body !== undefined) {
        config.data = options.body
      }

      // Make the request using Axios
      const response = await this.axiosInstance.request(config)

      // Return the response data
      return response.data as T
    } catch (error) {
      // Error handling is done by the response interceptor
      // Just re-throw HttpClientError or convert unknown errors
      if (error instanceof HttpClientError) {
        throw error
      }

      if (error instanceof Error) {
        throw new HttpClientError(`Request failed: ${error.message}`)
      }

      throw new HttpClientError("Unknown error occurred")
    }
  }

  async get<T>(url: string, options: Omit<RequestOptions, "method"> = {}): Promise<T> {
    return this.request<T>(url, { ...options, method: "GET" })
  }

  async post<T>(url: string, body?: any, options: Omit<RequestOptions, "method" | "body"> = {}): Promise<T> {
    return this.request<T>(url, { ...options, method: "POST", body })
  }

  async put<T>(url: string, body?: any, options: Omit<RequestOptions, "method" | "body"> = {}): Promise<T> {
    return this.request<T>(url, { ...options, method: "PUT", body })
  }

  async delete<T>(url: string, options: Omit<RequestOptions, "method"> = {}): Promise<T> {
    return this.request<T>(url, { ...options, method: "DELETE" })
  }

  /**
   * Get the underlying Axios instance for advanced configuration if needed
   * This allows for custom interceptors, advanced configuration, etc.
   */
  getAxiosInstance(): AxiosInstance {
    return this.axiosInstance
  }

  /**
   * Set default headers that will be applied to all requests
   */
  setDefaultHeaders(headers: Record<string, string>): void {
    Object.assign(this.axiosInstance.defaults.headers, headers)
  }

  /**
   * Set the base URL for all requests
   */
  setBaseURL(baseURL: string): void {
    this.axiosInstance.defaults.baseURL = baseURL
  }

  /**
   * Set the default timeout for all requests
   */
  setTimeout(timeout: number): void {
    this.axiosInstance.defaults.timeout = timeout
  }
}
