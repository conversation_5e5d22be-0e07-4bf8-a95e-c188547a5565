/**
 * Repository layer for comment data access operations
 * Manages all comment-related database operations
 */

import { MeetingComment, type IMeetingCommentDocument } from "@/db/models/meeting-comment"

export class CommentRepositoryError extends Error {
  constructor(
    message: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = "CommentRepositoryError"
  }
}

export interface CreateCommentData {
  meetId: number
  comment: string
  userId: string
}

export interface ICommentRepository {
  createComment(commentData: CreateCommentData): Promise<IMeetingCommentDocument>
  getCommentsByMeetId(meetId: number): Promise<IMeetingCommentDocument[]>
  getCommentsByUserId(userId: string): Promise<IMeetingCommentDocument[]>
  getCommentById(commentId: string): Promise<IMeetingCommentDocument | null>
  deleteCommentById(commentId: string, userId?: string): Promise<boolean>
  deleteCommentsByMeetId(meetId: number): Promise<number>
  updateComment(commentId: string, newComment: string, userId?: string): Promise<IMeetingCommentDocument | null>
  getCommentsWithPagination({
    meetId,
    meetErpCode,
    userId,
    page,
    limit,
  }: {
    meetId?: number
    meetErpCode?: string
    userId?: string
    page?: number
    limit?: number
  }): Promise<{
    comments: IMeetingCommentDocument[]
    total: number
    page: number
    limit: number
    totalPages: number
  }>
  countCommentsByMeetId(meetId: number): Promise<number>
  countCommentsByUserId(userId: string): Promise<number>
  getRecentComments(limit?: number): Promise<IMeetingCommentDocument[]>
  searchComments(searchTerm: string, options?: { meetId?: number; userId?: string }): Promise<IMeetingCommentDocument[]>
  commentExists(commentId: string): Promise<boolean>
  getCommentsByDateRange(startDate: Date, endDate: Date): Promise<IMeetingCommentDocument[]>
}

export class CommentRepository implements ICommentRepository {
  /**
   * Create a new comment
   */
  async createComment(commentData: CreateCommentData): Promise<IMeetingCommentDocument> {
    try {
      const newComment = new MeetingComment({
        meetId: commentData.meetId,
        comment: commentData.comment.trim(),
        userId: commentData.userId,
      })

      return await newComment.save()
    } catch (error) {
      throw new CommentRepositoryError("Failed to create comment", error as Error)
    }
  }

  /**
   * Get comments for a meeting by ID (primary method)
   */
  async getCommentsByMeetId(meetId: number): Promise<IMeetingCommentDocument[]> {
    try {
      return await (MeetingComment as any).findByMeetingId(meetId)
    } catch (error) {
      throw new CommentRepositoryError(`Failed to get comments for meeting: ${meetId}`, error as Error)
    }
  }

  /**
   * Get comments by user ID
   */
  async getCommentsByUserId(userId: string): Promise<IMeetingCommentDocument[]> {
    try {
      return await (MeetingComment as any).findByUserId(userId)
    } catch (error) {
      throw new CommentRepositoryError(`Failed to get comments for user: ${userId}`, error as Error)
    }
  }

  /**
   * Get a comment by its ID
   */
  async getCommentById(commentId: string): Promise<IMeetingCommentDocument | null> {
    try {
      return await MeetingComment.findById(commentId).exec()
    } catch (error) {
      throw new CommentRepositoryError(`Failed to get comment: ${commentId}`, error as Error)
    }
  }

  /**
   * Delete a comment by ID
   */
  async deleteCommentById(commentId: string, userId?: string): Promise<boolean> {
    try {
      const query: any = { _id: commentId }

      // If userId is provided, ensure user can only delete their own comments
      if (userId) {
        query.userId = userId
      }

      const result = await MeetingComment.deleteOne(query).exec()
      return result.deletedCount > 0
    } catch (error) {
      throw new CommentRepositoryError(`Failed to delete comment: ${commentId}`, error as Error)
    }
  }

  /**
   * Delete all comments for a meeting by ID
   */
  async deleteCommentsByMeetId(meetId: number): Promise<number> {
    try {
      const result = await (MeetingComment as any).deleteByMeetingId(meetId)
      return result.deletedCount || 0
    } catch (error) {
      throw new CommentRepositoryError(`Failed to delete comments for meeting: ${meetId}`, error as Error)
    }
  }

  /**
   * Update a comment
   */
  async updateComment(commentId: string, newComment: string, userId?: string): Promise<IMeetingCommentDocument | null> {
    try {
      const query: any = { _id: commentId }

      // If userId is provided, ensure user can only update their own comments
      if (userId) {
        query.userId = userId
      }

      const updatedComment = await MeetingComment.findOneAndUpdate(
        query,
        { $set: { comment: newComment.trim() } },
        { new: true, runValidators: true }
      ).exec()

      return updatedComment
    } catch (error) {
      throw new CommentRepositoryError(`Failed to update comment: ${commentId}`, error as Error)
    }
  }

  /**
   * Get comments with pagination
   */
  async getCommentsWithPagination(
    options: {
      meetId?: number
      meetErpCode?: string
      userId?: string
      page?: number
      limit?: number
    } = {}
  ): Promise<{
    comments: IMeetingCommentDocument[]
    total: number
    page: number
    limit: number
    totalPages: number
  }> {
    try {
      const page = options.page || 1
      const limit = Math.min(options.limit || 10, 100) // Max 100 per page
      const skip = (page - 1) * limit

      // Build query
      const query: any = {}

      if (options.meetId) {
        query.meetId = options.meetId
      }

      if (options.meetErpCode) {
        query.meetErpCode = options.meetErpCode
      }

      if (options.userId) {
        query.userId = options.userId
      }

      // Execute query with pagination
      const [comments, total] = await Promise.all([
        MeetingComment.find(query).sort({ createdAt: -1 }).skip(skip).limit(limit).exec(),
        MeetingComment.countDocuments(query).exec(),
      ])

      const totalPages = Math.ceil(total / limit)

      return {
        comments,
        total,
        page,
        limit,
        totalPages,
      }
    } catch (error) {
      throw new CommentRepositoryError("Failed to get comments with pagination", error as Error)
    }
  }

  /**
   * Count comments for a meeting by ID
   */
  async countCommentsByMeetId(meetId: number): Promise<number> {
    try {
      return await MeetingComment.countDocuments({ meetId }).exec()
    } catch (error) {
      throw new CommentRepositoryError(`Failed to count comments for meeting: ${meetId}`, error as Error)
    }
  }

  /**
   * Count comments by user
   */
  async countCommentsByUserId(userId: string): Promise<number> {
    try {
      return await MeetingComment.countDocuments({ userId }).exec()
    } catch (error) {
      throw new CommentRepositoryError(`Failed to count comments for user: ${userId}`, error as Error)
    }
  }

  /**
   * Get recent comments across all meetings
   */
  async getRecentComments(limit: number = 10): Promise<IMeetingCommentDocument[]> {
    try {
      return await MeetingComment.find({})
        .sort({ createdAt: -1 })
        .limit(Math.min(limit, 100)) // Max 100 comments
        .exec()
    } catch (error) {
      throw new CommentRepositoryError("Failed to get recent comments", error as Error)
    }
  }

  /**
   * Search comments by content
   */
  async searchComments(
    searchTerm: string,
    options: { meetId?: number; userId?: string } = {}
  ): Promise<IMeetingCommentDocument[]> {
    try {
      const query: any = {
        comment: { $regex: searchTerm, $options: "i" },
      }

      if (options.meetId) {
        query.meetId = options.meetId
      }

      if (options.userId) {
        query.userId = options.userId
      }

      return await MeetingComment.find(query).sort({ createdAt: -1 }).exec()
    } catch (error) {
      throw new CommentRepositoryError(`Failed to search comments: ${searchTerm}`, error as Error)
    }
  }

  /**
   * Check if a comment exists
   */
  async commentExists(commentId: string): Promise<boolean> {
    try {
      const count = await MeetingComment.countDocuments({ _id: commentId }).exec()
      return count > 0
    } catch (error) {
      throw new CommentRepositoryError(`Failed to check if comment exists: ${commentId}`, error as Error)
    }
  }

  /**
   * Get comments by date range
   */
  async getCommentsByDateRange(startDate: Date, endDate: Date): Promise<IMeetingCommentDocument[]> {
    try {
      return await MeetingComment.find({
        createdAt: {
          $gte: startDate,
          $lte: endDate,
        },
      })
        .sort({ createdAt: -1 })
        .exec()
    } catch (error) {
      throw new CommentRepositoryError(`Failed to get comments by date range`, error as Error)
    }
  }
}
