/**
 * Repository layer for meeting data access operations
 * Abstracts all MongoDB operations for meetings
 */

import { Meeting, type IMeetingDocument } from "@/db/models/meeting"
import type { PartnerType } from "@/types/meeting"
import type { ProcessedMeetingData } from "@/schemas/meeting"

export class MeetingRepositoryError extends Error {
  constructor(
    message: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = "MeetingRepositoryError"
  }
}

export interface GetMeetingsOptions {
  page?: number
  limit?: number
  startDate?: string
  endDate?: string
  partnerType?: PartnerType
  meetStatus?: string
  companyName?: string
}

export interface GetMeetingsResult {
  meetings: IMeetingDocument[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface IMeetingRepository {
  saveMeetings(meetingsData: ProcessedMeetingData[]): Promise<IMeetingDocument[]>
  getMeetings(options: GetMeetingsOptions): Promise<GetMeetingsResult>
  getMeetingById(meetId: number): Promise<IMeetingDocument | null>
  existsById(meetId: number): Promise<boolean>
  getMeetingsByDateRange(startDate: string, endDate: string): Promise<IMeetingDocument[]>
}

export class MeetingRepository implements IMeetingRepository {
  /**
   * Save multiple meeting documents to database
   */
  async saveMeetings(meetingsData: ProcessedMeetingData[]): Promise<IMeetingDocument[]> {
    try {
      const savedMeetings: IMeetingDocument[] = []

      for (const meetingData of meetingsData) {
        try {
          // Check if meeting already exists by meetId (primary key)
          const existingMeeting = await Meeting.findOne({ meetId: meetingData.meetId }).exec()

          if (existingMeeting) {
            // Update existing meeting with new data
            Object.assign(existingMeeting, meetingData)
            const updatedMeeting = await existingMeeting.save()
            savedMeetings.push(updatedMeeting)
          } else {
            // Create new meeting
            const newMeeting = new Meeting(meetingData)
            const savedMeeting = await newMeeting.save()
            savedMeetings.push(savedMeeting)
          }
        } catch (error) {
          // If checking for existing meeting fails, try to create a new one
          console.warn(`Failed to check existing meeting ${meetingData.meetId}, creating new:`, error)
          try {
            const newMeeting = new Meeting(meetingData)
            const savedMeeting = await newMeeting.save()
            savedMeetings.push(savedMeeting)
          } catch (saveError) {
            console.error(`Failed to save meeting ${meetingData.meetId}:`, saveError)
            // Continue with next meeting instead of failing the entire batch
          }
        }
      }

      return savedMeetings
    } catch (error) {
      throw new MeetingRepositoryError("Failed to save meetings", error as Error)
    }
  }

  /**
   * Get meetings with pagination and filtering
   */
  async getMeetings(options: GetMeetingsOptions = {}): Promise<GetMeetingsResult> {
    try {
      const page = options.page || 1
      const limit = Math.min(options.limit || 10, 100) // Max 100 per page
      const skip = (page - 1) * limit

      // Build query
      const query: any = {}

      if (options.startDate || options.endDate) {
        query.meetDate = {}
        if (options.startDate) query.meetDate.$gte = options.startDate
        if (options.endDate) query.meetDate.$lte = options.endDate
      }

      if (options.partnerType) {
        query.partnerType = options.partnerType
      }

      if (options.meetStatus) {
        query.meetStatus = options.meetStatus
      }

      if (options.companyName) {
        query.companyName = { $regex: options.companyName, $options: "i" }
      }

      // Execute query with pagination
      const [meetings, total] = await Promise.all([
        Meeting.find(query).sort({ meetDate: -1, meetTime: -1 }).skip(skip).limit(limit).exec(),
        Meeting.countDocuments(query).exec(),
      ])

      const totalPages = Math.ceil(total / limit)

      return {
        meetings,
        total,
        page,
        limit,
        totalPages,
      }
    } catch (error) {
      throw new MeetingRepositoryError("Failed to get meetings", error as Error)
    }
  }

  /**
   * Get meeting by ID (primary method)
   */
  async getMeetingById(meetId: number): Promise<IMeetingDocument | null> {
    try {
      return await Meeting.findOne({ meetId }).exec()
    } catch (error) {
      throw new MeetingRepositoryError(`Failed to get meeting by ID: ${meetId}`, error as Error)
    }
  }

  /**
   * Check if meeting exists by ID
   */
  async existsById(meetId: number): Promise<boolean> {
    try {
      const count = await Meeting.countDocuments({ meetId }).exec()
      return count > 0
    } catch (error) {
      throw new MeetingRepositoryError(`Failed to check if meeting exists: ${meetId}`, error as Error)
    }
  }

  /**
   * Get meetings by date range
   */
  async getMeetingsByDateRange(startDate: string, endDate: string): Promise<IMeetingDocument[]> {
    try {
      return await Meeting.find({
        meetDate: {
          $gte: startDate,
          $lte: endDate,
        },
      })
        .sort({ meetDate: -1, meetTime: -1 })
        .exec()
    } catch (error) {
      throw new MeetingRepositoryError(
        `Failed to get meetings by date range: ${startDate} to ${endDate}`,
        error as Error
      )
    }
  }
}
