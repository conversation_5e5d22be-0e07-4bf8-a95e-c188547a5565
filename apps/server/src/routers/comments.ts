/**
 * tRPC router for comment management endpoints
 * Handles all comment-related operations
 */

import { CommentRepository, MeetingRepository } from "@/repositories"

import { publicProcedure, router } from "@/lib/trpc"
import { addCommentByIdSchema, deleteCommentSchema, getCommentsByIdSchema } from "@/schemas/meeting"
import { CommentService } from "@/services/comment-service"
import { CVMApiService } from "@/services/cvm-api"
import { MeetingService } from "@/services/meeting-service"
import { CommentController } from "@/controllers/comment-controller"

// Initialize controllers
const cvmApiService = new CVMApiService()
const meetingRepository = new MeetingRepository()
const commentRepository = new CommentRepository()
const commentService = new CommentService(meetingRepository, commentRepository)
const meetingService = new MeetingService(cvmApiService, meetingRepository)
const commentController = new CommentController(meetingService, commentService)

export const commentsRouter = router({
  /**
   * Add a comment to a meeting by ID
   */
  addCommentById: publicProcedure.input(addCommentByIdSchema).mutation(async ({ input }) => {
    return await commentController.addCommentById(input)
  }),

  /**
   * Get comments for a meeting by ID
   */
  getCommentsById: publicProcedure.input(getCommentsByIdSchema).query(async ({ input }) => {
    return await commentController.getCommentsById(input)
  }),

  /**
   * Delete a comment
   */
  deleteComment: publicProcedure.input(deleteCommentSchema).mutation(async ({ input }) => {
    return await commentController.deleteComment(input)
  }),
})
