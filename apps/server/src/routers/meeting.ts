/**
 * tRPC router for core meeting operations
 * Handles meeting CRUD operations, statistics, and metadata
 */

import { CommentRepository, MeetingRepository } from "@/repositories"
import { TRPCError } from "@trpc/server"

import { PartnerType } from "@/types/meeting"
import { publicProcedure, router } from "@/lib/trpc"
import { getMeetingByMeetIdSchema, getMeetingsQuerySchema } from "@/schemas/meeting"
import { CommentService } from "@/services/comment-service"
import { CVMApiService } from "@/services/cvm-api"
import { MeetingService, MeetingServiceError } from "@/services/meeting-service"
import { MeetingController } from "@/controllers/meeting-controller"

// Initialize controllers
const cvmApiService = new CVMApiService()
const meetingRepository = new MeetingRepository()
const commentRepository = new CommentRepository()
const commentService = new CommentService(meetingRepository, commentRepository)
const meetingService = new MeetingService(cvmApiService, meetingRepository)
const meetingController = new MeetingController(meetingService)

export const meetingRouter = router({
  /**
   * Get meetings from database with pagination and filtering
   */
  getMeetings: publicProcedure.input(getMeetingsQuerySchema).query(async ({ input }) => {
    return await meetingController.getMeetings(input)
  }),

  /**
   * Get meeting by ID (primary method)
   */
  getMeetingById: publicProcedure.input(getMeetingByMeetIdSchema).query(async ({ input }) => {
    return await meetingController.getMeetingById(input.meetId)
  }),

  /**
   * Get meeting statistics
   */
  getStatistics: publicProcedure.query(async () => {
    try {
      const stats = await meetingService.getMeetingStatistics()

      return {
        success: true,
        data: stats,
      }
    } catch (error) {
      if (error instanceof MeetingServiceError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
          cause: error,
        })
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get statistics",
      })
    }
  }),

  /**
   * Get partner types enum values
   */
  getPartnerTypes: publicProcedure.query(() => {
    return {
      success: true,
      data: Object.values(PartnerType),
    }
  }),
})
