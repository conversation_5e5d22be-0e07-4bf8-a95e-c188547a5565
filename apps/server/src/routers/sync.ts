/**
 * tRPC router for data synchronization endpoints
 * Handles external API integration and data sync operations
 */

import { MeetingRepository } from "@/repositories"
import { TRPCError } from "@trpc/server"
import { z } from "zod"

import { publicProcedure, router } from "@/lib/trpc"
import { fetchMeetingDataInputSchema } from "@/schemas/cvm-api"
import { CVMApiService } from "@/services/cvm-api"
import { MeetingService, MeetingServiceError } from "@/services/meeting-service"
import { MeetingController } from "@/controllers/meeting-controller"

// Initialize controllers
const cvmApiService = new CVMApiService()
const meetingRepository = new MeetingRepository()
const meetingService = new MeetingService(cvmApiService, meetingRepository)
const meetingController = new MeetingController(meetingService)

export const syncRouter = router({
  /**
   * Fetch and process meeting data from CVM API
   */
  fetchAndProcess: publicProcedure.input(fetchMeetingDataInputSchema).mutation(async ({ input }) => {
    return await meetingController.fetchAndProcessMeetingData(input)
  }),

  /**
   * Fetch, process, and save meeting data to database
   */
  fetchAndSave: publicProcedure.input(fetchMeetingDataInputSchema).mutation(async ({ input }) => {
    return await meetingController.fetchAndSaveMeetingData(input)
  }),
})
