/**
 * Zod schemas for CVM API response validation
 */

import { z } from "zod"

// CVM Meeting Info API Response Schema
export const cvmMeetingInfoResponseSchema = z.object({
  meet_id: z.number(),
  add_meet_date: z.string(),
  add_meet_time: z.string(),
  meet_erp_code: z.string(),
  company_name_th: z.string(),
  meet_block_name: z.string(),
  meet_sum_grade: z.string(), // API returns string, not number
  site: z.string(),
  meet_status: z.string().nullable(),
  meet_create_at: z.string(),
  cus_name: z.string(),
  cus_position: z.string(),
  cus_phone: z.string(),
  cus_email: z.string(),
  cus_conference: z.string().nullable(),
  cus_other_desc: z.string(),
  type_of_business_id: z.string(), // API returns string, not number
  cus_clc: z.string().nullable(),
  cus_visit: z.string().nullable(),
  cus_on_site_other: z.string(),
  plc: z.string().nullable(),
  cus_address_company: z.string(),
  cus_districts: z.string().nullable(),
  cus_amphurs: z.string().nullable(),
  cus_provinces: z.string().nullable(),
  cus_post: z.string(),
  sale_name_th: z.string(),
  meet_department_sale: z.string(),
  ps_clc_name: z.string().nullable(),
  ps_visit_name: z.string().nullable(),
  ps_plc_name: z.string().nullable(),
  private_customer_transform_businesses: z.string().nullable(),
  sale_type_name: z.string().nullable(),

  // Additional fields
  tax_id: z.string().optional(),
  sale_mom_desc: z.string().optional(),
  presale_mom_desc: z.string().optional(),
  service_name: z.string().optional(),
  service_status: z.string().optional(),
  service_status_description: z.string().optional(),
  partner_erp: z.string().nullable(),
  service_erp: z.string().nullable(),
  partner_crm: z.string().nullable(),
  service_crm: z.string().nullable(),
  partner_hrm: z.string().nullable(),
  service_hrm: z.string().nullable(),
  partner_supply_chain: z.string().nullable(),
  service_supply_chain: z.string().nullable(),
  partner_workflow: z.string().nullable(),
  service_workflow: z.string().nullable(),
  partner_e_commerce_web: z.string().nullable(),
  service_e_commerce_web: z.string().nullable(),
})

// CVM MOM Info API Response Schema
export const cvmMOMInfoResponseSchema = z.object({
  meet_id: z.number(),
  sale_mom_desc: z.string(),
  presale_mom_desc: z.string(),
  StageServices: z.array(
    z.object({
      service_name: z.string(),
      service_status: z.string(),
      service_status_description: z.string(),
    })
  ),
})

// API Response Wrapper Schemas
export const cvmMeetingInfoApiResponseSchema = z.object({
  status: z.number(),
  message: z.string(),
  data: z.array(cvmMeetingInfoResponseSchema).nullable(),
})

export const cvmMOMInfoApiResponseSchema = z.object({
  status: z.number(),
  message: z.string(),
  data: z.array(cvmMOMInfoResponseSchema).nullable(),
})

// Input validation schemas for API calls
export const fetchMeetingDataInputSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
  meetId: z.coerce.number().optional(),
})

// Export types
export type CVMMeetingInfoResponse = z.infer<typeof cvmMeetingInfoResponseSchema>
export type CVMMOMInfoResponse = z.infer<typeof cvmMOMInfoResponseSchema>
export type CVMMeetingInfoApiResponse = z.infer<typeof cvmMeetingInfoApiResponseSchema>
export type CVMMOMInfoApiResponse = z.infer<typeof cvmMOMInfoApiResponseSchema>
export type FetchMeetingDataInput = z.infer<typeof fetchMeetingDataInputSchema>
