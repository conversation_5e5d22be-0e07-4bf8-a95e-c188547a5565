/**
 * Zod schemas for processed meeting data validation
 */

import { z } from "zod"

import { PartnerType } from "@/types/meeting"

// Processed Meeting Data Schema
export const processedMeetingDataSchema = z.object({
  // Basic Meeting Information
  meetDate: z.string(),
  meetTime: z.string(),
  meetId: z.number(), // Primary key for data operations
  meetErpCode: z.string(), // Display field
  companyName: z.string(),
  block: z.string(),
  sumGrade: z.string(),
  meetingChannel: z.string(),
  meetStatus: z.string(),
  meetingCreatedAt: z.string(),

  // Customer Information
  customerName: z.string(),
  customerPosition: z.string(),
  customerPhone: z.string(),
  customerEmail: z.string(),
  customerConference: z.string(),
  customerOtherDesc: z.string(),
  typeOfBusiness: z.string(),
  customerCLC: z.string().nullable(),
  customerVisit: z.string(),
  customerOnsiteOther: z.string(),
  customerPLC: z.string().nullable(),

  // Address Information
  customerAddressCompany: z.string(),
  subDistrict: z.string(),
  district: z.string(),
  province: z.string(),
  postalCode: z.string(),

  // Sales Information
  saleName: z.string(),
  blockTeamSale: z.string(),
  presaleCLCName: z.string(),
  presaleVisitName: z.string(),
  presalePLCName: z.string(),
  invoiceHeader: z.string(),

  // Additional CVM Fields
  taxId: z.string().optional(),
  saleMOM: z.string().optional(),
  presaleMOM: z.string().optional(),
  service: z.string().optional(),
  statusService: z.string().optional(),
  partnerERP: z.string().optional(),
  serviceERP: z.string().optional(),
  partnerCRM: z.string().optional(),
  serviceCRM: z.string().optional(),
  partnerHRM: z.string().optional(),
  serviceHRM: z.string().optional(),
  partnerSupplyChain: z.string().optional(),
  serviceSupplyChain: z.string().optional(),
  partnerWorkflow: z.string().optional(),
  serviceWorkflow: z.string().optional(),
  partnerEcommerceWeb: z.string().optional(),
  serviceEcommerceWeb: z.string().optional(),

  // Computed Fields
  partnerType: z.enum(PartnerType),
  privilege: z.string(),
  presalesPrivilegeName: z.string(),

  // Metadata
  processedAt: z.date(),
})

// Database Document Schema
export const meetingDocumentSchema = processedMeetingDataSchema.extend({
  _id: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// Query Schemas
export const getMeetingsQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  partnerType: z.enum(PartnerType).optional(),
  meetStatus: z.string().optional(),
  companyName: z.string().optional(),
})

export const getMeetingByIdSchema = z.object({
  id: z.string(),
})

export const getMeetingByMeetIdSchema = z.object({
  meetId: z.number(),
})

// User Comment Schemas
export const addCommentByIdSchema = z.object({
  meetId: z.number(),
  comment: z.string().min(1).max(1000),
  userId: z.string(),
})

export const addCommentSchema = z.object({
  meetErpCode: z.string(),
  comment: z.string().min(1).max(1000),
  userId: z.string(),
})

export const getCommentsByIdSchema = z.object({
  meetId: z.number(),
})

export const getCommentsSchema = z.object({
  meetErpCode: z.string(),
})

export const deleteCommentSchema = z.object({
  commentId: z.string(),
})

// Meeting Comment Schema
export const meetingCommentSchema = z.object({
  _id: z.string().optional(),
  userId: z.string(),
  comment: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// Export types
export type ProcessedMeetingData = z.infer<typeof processedMeetingDataSchema>
export type MeetingDocument = z.infer<typeof meetingDocumentSchema>
export type GetMeetingsQuery = z.infer<typeof getMeetingsQuerySchema>
export type GetMeetingById = z.infer<typeof getMeetingByIdSchema>
export type AddComment = z.infer<typeof addCommentSchema>
export type GetComments = z.infer<typeof getCommentsSchema>
export type DeleteComment = z.infer<typeof deleteCommentSchema>
export type MeetingComment = z.infer<typeof meetingCommentSchema>
