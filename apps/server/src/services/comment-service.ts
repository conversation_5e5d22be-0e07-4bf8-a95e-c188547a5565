/**
 * Comment service that manages comment operations
 * Now uses repository layer for data access
 */

import type { IMeetingCommentDocument } from "@/db/models/meeting-comment"
import type { ICommentRepository } from "@/repositories/comment-repository"
import type { IMeetingRepository } from "@/repositories/meeting-repository"
import { MeetingServiceError } from "@/services/meeting-service"

export class CommentServiceError extends Error {
  constructor(
    message: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = "CommentServiceError"
  }
}

export interface ICommentService {
  addCommentById(meetId: number, comment: string, userId: string): Promise<IMeetingCommentDocument>
  getCommentsById(meetId: number): Promise<IMeetingCommentDocument[]>
  deleteComment(commentId: string, userId?: string): Promise<boolean>
  getCommentsByUser(userId: string): Promise<IMeetingCommentDocument[]>
}

export class CommentService implements ICommentService {
  private meetingRepository: IMeetingRepository
  private commentRepository: ICommentRepository

  constructor(meetingRepository: IMeetingRepository, commentRepository: ICommentRepository) {
    this.commentRepository = commentRepository
    this.meetingRepository = meetingRepository
  }

  // Comment Management Methods

  /**
   * Add a comment to a meeting by ID (primary method)
   */
  async addCommentById(meetId: number, comment: string, userId: string): Promise<IMeetingCommentDocument> {
    try {
      // Verify meeting exists
      const meeting = await this.meetingRepository.getMeetingById(meetId)
      if (!meeting) {
        throw new MeetingServiceError(`Meeting with ID ${meetId} not found`)
      }

      return await this.commentRepository.createComment({
        meetId,
        comment,
        userId,
      })
    } catch (error) {
      throw new MeetingServiceError(`Failed to add comment to meeting: ${meetId}`, error as Error)
    }
  }

  /**
   * Get comments for a meeting by ID (primary method)
   */
  async getCommentsById(meetId: number): Promise<IMeetingCommentDocument[]> {
    try {
      return await this.commentRepository.getCommentsByMeetId(meetId)
    } catch (error) {
      throw new MeetingServiceError(`Failed to get comments for meeting: ${meetId}`, error as Error)
    }
  }

  /**
   * Delete a comment
   */
  async deleteComment(commentId: string, userId?: string): Promise<boolean> {
    try {
      return await this.commentRepository.deleteCommentById(commentId, userId)
    } catch (error) {
      throw new MeetingServiceError(`Failed to delete comment: ${commentId}`, error as Error)
    }
  }

  /**
   * Get comments by user
   */
  async getCommentsByUser(userId: string): Promise<IMeetingCommentDocument[]> {
    try {
      return await this.commentRepository.getCommentsByUserId(userId)
    } catch (error) {
      throw new MeetingServiceError(`Failed to get comments for user: ${userId}`, error as Error)
    }
  }
}
