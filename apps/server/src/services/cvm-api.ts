/**
 * CVM API integration service
 */

import { HttpClient, HttpClientError } from "@/lib/http-client"
import {
  cvmMeetingInfoApiResponseSchema,
  cvmMOMInfoApiResponseSchema,
  type CVMMeetingInfoResponse,
  type CVMMOMInfoResponse,
  type FetchMeetingDataInput,
} from "@/schemas/cvm-api"

export class CVMApiError extends Error {
  constructor(
    message: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = "CVMApiError"
  }
}

export interface ICVMApiService {
  fetchMeetingInfo(params: FetchMeetingDataInput): Promise<CVMMeetingInfoResponse[]>
  fetchMOMInfo(params: FetchMeetingDataInput): Promise<CVMMOMInfoResponse[]>
  fetchAllMeetingData(params: FetchMeetingDataInput): Promise<{
    meetingInfo: CVMMeetingInfoResponse[]
    momInfo: CVMMOMInfoResponse[]
  }>
  createMOMInfoMap(momInfoList: CVMMOMInfoResponse[]): Map<number, CVMMOMInfoResponse>
}

export class CVMApiService implements ICVMApiService {
  private httpClient: HttpClient

  constructor() {
    const baseURL = process.env.CVM_API_BASE_URL
    const clientId = process.env.CVM_API_CLIENT_ID
    const clientSecret = process.env.CVM_API_CLIENT_SECRET

    if (!baseURL || !clientId || !clientSecret) {
      throw new Error("CVM API configuration is missing. Please check environment variables.")
    }

    this.httpClient = new HttpClient({
      baseURL,
      headers: {
        "Client-ID": clientId,
        "Client-Secret": clientSecret,
        "Content-Type": "application/json",
      },
      timeout: 30000, // 30 seconds
    })
  }

  /**
   * Fetch meeting information from CVM API
   */
  async fetchMeetingInfo(params: FetchMeetingDataInput = {}): Promise<CVMMeetingInfoResponse[]> {
    try {
      // Build request body for POST request
      const requestBody: any = {}

      if (params.startDate) {
        requestBody.start_date = params.startDate
      }
      if (params.endDate) {
        requestBody.end_date = params.endDate
      }
      if (params.startTime) {
        requestBody.start_time = params.startTime
      }
      if (params.endTime) {
        requestBody.end_time = params.endTime
      }

      const url = `/on_period_meet_info`

      const response = await this.httpClient.post(url, requestBody)

      // Validate response structure
      const validatedResponse = cvmMeetingInfoApiResponseSchema.parse(response)

      if (validatedResponse.status !== 200) {
        throw new CVMApiError(`API returned error status: ${validatedResponse.status}`)
      }

      if (!validatedResponse.data) {
        // No data is acceptable, return empty array
        return []
      }

      return validatedResponse.data
    } catch (error) {
      if (error instanceof CVMApiError) {
        throw error
      }

      if (error instanceof HttpClientError) {
        throw new CVMApiError(`HTTP request failed: ${error.message}`, error)
      }

      if (error instanceof Error) {
        throw new CVMApiError(`Failed to fetch meeting info: ${error.message}`, error)
      }

      throw new CVMApiError("Unknown error occurred while fetching meeting info")
    }
  }

  /**
   * Fetch MOM information from CVM API
   */
  async fetchMOMInfo(params: FetchMeetingDataInput = {}): Promise<CVMMOMInfoResponse[]> {
    try {
      // Build request body for POST request
      const requestBody: any = {}

      if (params.startDate) {
        requestBody.start_date = params.startDate
      }
      if (params.endDate) {
        requestBody.end_date = params.endDate
      }
      if (params.startTime) {
        requestBody.start_time = params.startTime
      }
      if (params.endTime) {
        requestBody.end_time = params.endTime
      }

      const url = `/mom_info_by_meet/on_period`

      const response = await this.httpClient.post(url, requestBody)

      // Validate response structure
      const validatedResponse = cvmMOMInfoApiResponseSchema.parse(response)

      if (validatedResponse.status !== 200) {
        throw new CVMApiError(`API returned error status: ${validatedResponse.status}`)
      }

      if (!validatedResponse.data) {
        // MOM data might be optional, return empty array
        return []
      }

      return validatedResponse.data
    } catch (error) {
      if (error instanceof CVMApiError) {
        throw error
      }

      if (error instanceof HttpClientError) {
        throw new CVMApiError(`HTTP request failed: ${error.message}`, error)
      }

      if (error instanceof Error) {
        throw new CVMApiError(`Failed to fetch MOM info: ${error.message}`, error)
      }

      throw new CVMApiError("Unknown error occurred while fetching MOM info")
    }
  }

  /**
   * Fetch both meeting and MOM information in parallel
   */
  async fetchAllMeetingData(params: FetchMeetingDataInput = {}): Promise<{
    meetingInfo: CVMMeetingInfoResponse[]
    momInfo: CVMMOMInfoResponse[]
  }> {
    try {
      const [meetingInfo, momInfo] = await Promise.all([this.fetchMeetingInfo(params), this.fetchMOMInfo(params)])

      return {
        meetingInfo,
        momInfo,
      }
    } catch (error) {
      if (error instanceof CVMApiError) {
        throw error
      }

      throw new CVMApiError("Failed to fetch meeting data", error as Error)
    }
  }

  /**
   * Create a map of MOM info by meet_id for efficient lookup
   * Note: MOM API uses meet_id
   */
  createMOMInfoMap(momInfoList: CVMMOMInfoResponse[]): Map<number, CVMMOMInfoResponse> {
    const map = new Map<number, CVMMOMInfoResponse>()

    for (const momInfo of momInfoList) {
      map.set(momInfo.meet_id, momInfo)
    }

    return map
  }
}
