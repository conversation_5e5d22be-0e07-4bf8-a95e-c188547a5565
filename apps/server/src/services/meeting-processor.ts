/**
 * Data transformation logic for processing CVM API responses
 */

import {
  EXISTING_GROUP_BLOCKS,
  NEW_CUSTOMER_BUSINESSES,
  NEW_PARTNER_BUSINESSES,
  PartnerType,
  PRIVILEGE_MAPPING,
} from "@/types/meeting"
import type { CVMMeetingInfoResponse, CVMMOMInfoResponse } from "@/schemas/cvm-api"
import type { ProcessedMeetingData } from "@/schemas/meeting"

/**
 * Determines partner type based on business rules
 * Handles nullable privateCustomerTransformBusinesses field properly
 */
export function classifyPartnerType(
  meetBlockName: string,
  privateCustomerTransformBusinesses: string | null
): PartnerType {
  // Primary source: meet_block_name
  if (EXISTING_GROUP_BLOCKS.includes(meetBlockName)) {
    return PartnerType.EXISTING_GROUP
  }

  // Fallback to private_customer_transform_businesses (with null safety)
  if (privateCustomerTransformBusinesses && NEW_PARTNER_BUSINESSES.includes(privateCustomerTransformBusinesses)) {
    return PartnerType.NEW_PARTNER_GROUP
  }

  if (privateCustomerTransformBusinesses && NEW_CUSTOMER_BUSINESSES.includes(privateCustomerTransformBusinesses)) {
    return PartnerType.NEW_CUSTOMER_GROUP
  }

  // Default fallback - could be configurable
  return PartnerType.UNKNOWN
}

/**
 * Determines privilege based on sale_type_name
 * Handles edge cases and provides fallback values
 */
export function determinePrivilege(saleTypeName: string | null | undefined): string {
  if (!saleTypeName) {
    return "-"
  }

  return PRIVILEGE_MAPPING[saleTypeName as keyof typeof PRIVILEGE_MAPPING] || ""
}

/**
 * Determines presales privilege name based on privilege and available names
 * Handles null/undefined values gracefully
 */
export function determinePresalesPrivilegeName(
  privilege: string,
  psCLCName: string | null | undefined,
  psPLCName: string | null | undefined
): string {
  switch (privilege) {
    case "PLC":
      return psPLCName || ""
    case "CLC":
      return psCLCName || ""
    default:
      return "-"
  }
}

/**
 * Maps CVM API response fields to processed meeting data format
 */
export function mapMeetingFields(
  meetingInfo: CVMMeetingInfoResponse,
  momInfo?: CVMMOMInfoResponse
): Omit<
  ProcessedMeetingData,
  "partnerType" | "privilege" | "presalesPrivilegeName" | "processedAt" | "sourceData" | "userStatus" | "userNotes"
> {
  return {
    // Basic Meeting Information
    meetDate: meetingInfo.add_meet_date || "",
    meetTime: meetingInfo.add_meet_time || "",
    meetId: meetingInfo.meet_id, // Primary key for data operations
    meetErpCode: meetingInfo.meet_erp_code || "", // Display field
    companyName: meetingInfo.company_name_th || "",
    block: meetingInfo.meet_block_name || "",
    sumGrade: meetingInfo.meet_sum_grade,
    meetingChannel: meetingInfo.site || "",
    meetStatus: meetingInfo.meet_status || "",
    meetingCreatedAt: meetingInfo.meet_create_at || "",

    // Customer Information
    customerName: meetingInfo.cus_name || "",
    customerPosition: meetingInfo.cus_position || "",
    customerPhone: meetingInfo.cus_phone || "",
    customerEmail: meetingInfo.cus_email || "",
    customerConference: meetingInfo.cus_conference || "",
    customerOtherDesc: meetingInfo.cus_other_desc || "",
    typeOfBusiness: meetingInfo.type_of_business_id,
    customerCLC: meetingInfo.cus_clc,
    customerVisit: meetingInfo.cus_visit || "",
    customerOnsiteOther: meetingInfo.cus_on_site_other || "",
    customerPLC: meetingInfo.plc,

    // Address Information
    customerAddressCompany: meetingInfo.cus_address_company || "",
    subDistrict: meetingInfo.cus_districts || "",
    district: meetingInfo.cus_amphurs || "",
    province: meetingInfo.cus_provinces || "",
    postalCode: meetingInfo.cus_post || "",

    // Sales Information
    saleName: meetingInfo.sale_name_th || "",
    blockTeamSale: meetingInfo.meet_department_sale || "",
    presaleCLCName: meetingInfo.ps_clc_name || "",
    presaleVisitName: meetingInfo.ps_visit_name || "",
    presalePLCName: meetingInfo.ps_plc_name || "",
    invoiceHeader: meetingInfo.private_customer_transform_businesses || "",

    // Additional CVM Fields
    taxId: meetingInfo.tax_id,
    saleMOM: momInfo?.sale_mom_desc || meetingInfo.sale_mom_desc,
    presaleMOM: momInfo?.presale_mom_desc || meetingInfo.presale_mom_desc,
    service: momInfo?.StageServices?.[0]?.service_name || meetingInfo.service_name,
    statusService: momInfo?.StageServices?.[0]
      ? `${momInfo.StageServices[0].service_status} - ${momInfo.StageServices[0].service_status_description}`
      : meetingInfo.service_status && meetingInfo.service_status_description
        ? `${meetingInfo.service_status} - ${meetingInfo.service_status_description}`
        : meetingInfo.service_status || meetingInfo.service_status_description,
    partnerERP: meetingInfo.partner_erp || "",
    serviceERP: meetingInfo.service_erp || "",
    partnerCRM: meetingInfo.partner_crm || "",
    serviceCRM: meetingInfo.service_crm || "",
    partnerHRM: meetingInfo.partner_hrm || "",
    serviceHRM: meetingInfo.service_hrm || "",
    partnerSupplyChain: meetingInfo.partner_supply_chain || "",
    serviceSupplyChain: meetingInfo.service_supply_chain || "",
    partnerWorkflow: meetingInfo.partner_workflow || "",
    serviceWorkflow: meetingInfo.service_workflow || "",
    partnerEcommerceWeb: meetingInfo.partner_e_commerce_web || "",
    serviceEcommerceWeb: meetingInfo.service_e_commerce_web || "",
  }
}

/**
 * Main function to process CVM API data into structured meeting data
 * Includes comprehensive error handling and validation
 */
export function processMeetingData(
  meetingInfo: CVMMeetingInfoResponse,
  momInfo?: CVMMOMInfoResponse
): ProcessedMeetingData {
  try {
    // Validate required fields
    if (!meetingInfo.meet_id) {
      throw new Error("Missing required field: meet_id")
    }

    // Map basic fields
    const mappedFields = mapMeetingFields(meetingInfo, momInfo)

    // Apply business logic with proper null handling
    const partnerType = classifyPartnerType(
      meetingInfo.meet_block_name || "",
      meetingInfo.private_customer_transform_businesses
    )

    const privilege = determinePrivilege(meetingInfo.sale_type_name)

    const presalesPrivilegeName = determinePresalesPrivilegeName(
      privilege,
      meetingInfo.ps_clc_name,
      meetingInfo.ps_plc_name
    )

    return {
      ...mappedFields,
      partnerType,
      privilege,
      presalesPrivilegeName,
      processedAt: new Date(),
    }
  } catch (error) {
    // Log the error and re-throw with context
    console.error("Error processing meeting data:", {
      meetId: meetingInfo?.meet_id,
      meetErpCode: meetingInfo?.meet_erp_code, // Include for debugging
      error: error instanceof Error ? error.message : String(error),
    })
    throw error
  }
}

/**
 * Batch process multiple meeting records with error handling
 * Continues processing even if individual records fail
 */
export function processMeetingDataBatch(
  meetingInfoList: CVMMeetingInfoResponse[],
  momInfoMap: Map<number, CVMMOMInfoResponse> = new Map()
): ProcessedMeetingData[] {
  const results: ProcessedMeetingData[] = []
  const errors: Array<{ meetId: number | string; meetErpCode: string; error: string }> = []

  for (const meetingInfo of meetingInfoList) {
    try {
      const momInfo = momInfoMap.get(meetingInfo.meet_id)
      const processedData = processMeetingData(meetingInfo, momInfo)
      results.push(processedData)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      errors.push({
        meetId: meetingInfo.meet_id || "unknown",
        meetErpCode: meetingInfo.meet_erp_code || "unknown",
        error: errorMessage,
      })
      console.warn(`Failed to process meeting ${meetingInfo.meet_id} (${meetingInfo.meet_erp_code}):`, errorMessage)
    }
  }

  if (errors.length > 0) {
    console.warn(`Batch processing completed with ${errors.length} errors out of ${meetingInfoList.length} records`)
  }

  return results
}
