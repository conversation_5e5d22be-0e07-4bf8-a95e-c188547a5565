/**
 * Meeting service that combines CVM API integration with data processing
 * Now uses repository layer for data access
 */

import { MeetingRepository } from "@/repositories"

import { Meeting, type IMeetingDocument } from "@/db/models/meeting"
import type { IMeetingCommentDocument } from "@/db/models/meeting-comment"
import type { PartnerType } from "@/types/meeting"
import type { FetchMeetingDataInput } from "@/schemas/cvm-api"
import type { ProcessedMeetingData } from "@/schemas/meeting"
import type { ICommentRepository } from "@/repositories/comment-repository"
import type { GetMeetingsOptions, GetMeetingsResult, IMeetingRepository } from "@/repositories/meeting-repository"
import type { ICVMApiService } from "@/services/cvm-api"

import { CVMApiError } from "./cvm-api"
import { processMeetingDataBatch } from "./meeting-processor"

export class MeetingServiceError extends Error {
  constructor(
    message: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = "MeetingServiceError"
  }
}

export interface IMeetingService {
  fetchAndProcessMeetingData(params: FetchMeetingDataInput): Promise<ProcessedMeetingData[]>
  fetchAndProcessMeetingById(meetId: number): Promise<ProcessedMeetingData | null>
  saveMeetingData(processedData: ProcessedMeetingData[]): Promise<IMeetingDocument[]>
  fetchAndSaveMeetingData(params: FetchMeetingDataInput): Promise<IMeetingDocument[]>
  getMeetings(options: GetMeetingsOptions): Promise<GetMeetingsResult>
  getMeetingById(meetId: number): Promise<IMeetingDocument | null>
  getMeetingStatistics(): Promise<any>
}

export class MeetingService {
  private cvmApiService: ICVMApiService
  private meetingRepository: IMeetingRepository

  constructor(cvmApiService: ICVMApiService, meetingRepository: IMeetingRepository) {
    this.cvmApiService = cvmApiService
    this.meetingRepository = meetingRepository
  }

  /**
   * Fetch and process meeting data from CVM API
   */
  async fetchAndProcessMeetingData(params: FetchMeetingDataInput = {}): Promise<ProcessedMeetingData[]> {
    try {
      // Fetch data from both API endpoints
      const { meetingInfo, momInfo } = await this.cvmApiService.fetchAllMeetingData(params)

      if (meetingInfo.length === 0) {
        return []
      }

      // Create MOM info lookup map for efficient processing
      const momInfoMap = this.cvmApiService.createMOMInfoMap(momInfo)

      // Process the data using business logic
      const processedData = processMeetingDataBatch(meetingInfo, momInfoMap)

      return processedData
    } catch (error) {
      if (error instanceof CVMApiError) {
        throw new MeetingServiceError(`CVM API error: ${error.message}`, error)
      }

      if (error instanceof Error) {
        throw new MeetingServiceError(`Failed to fetch and process meeting data: ${error.message}`, error)
      }

      throw new MeetingServiceError("Unknown error occurred while processing meeting data")
    }
  }

  /**
   * Fetch and process meeting data for a specific meeting by ID
   */
  async fetchAndProcessMeetingById(meetId: number): Promise<ProcessedMeetingData | null> {
    try {
      const processedData = await this.fetchAndProcessMeetingData({ meetId })

      return processedData.length > 0 ? processedData[0] : null
    } catch (error) {
      if (error instanceof MeetingServiceError) {
        throw error
      }

      throw new MeetingServiceError(`Failed to fetch meeting by ID: ${meetId}`, error as Error)
    }
  }

  // Database Operations

  /**
   * Save processed meeting data to database (preserves user data during sync)
   */
  async saveMeetingData(processedData: ProcessedMeetingData[]): Promise<IMeetingDocument[]> {
    try {
      // Prepare data with preserved user data
      const dataToSave: ProcessedMeetingData[] = []

      for (const meetingData of processedData) {
        dataToSave.push(meetingData)
      }

      return await this.meetingRepository.saveMeetings(dataToSave)
    } catch (error) {
      throw new MeetingServiceError(`Failed to save meeting data: ${error}`, error as Error)
    }
  }

  /**
   * Fetch and save meeting data from CVM API
   */
  async fetchAndSaveMeetingData(params: FetchMeetingDataInput = {}): Promise<IMeetingDocument[]> {
    try {
      const processedData = await this.fetchAndProcessMeetingData(params)

      if (processedData.length === 0) {
        return []
      }

      return await this.saveMeetingData(processedData)
    } catch (error) {
      if (error instanceof MeetingServiceError) {
        throw error
      }

      throw new MeetingServiceError("Failed to fetch and save meeting data", error as Error)
    }
  }

  /**
   * Get meetings from database with pagination and filtering
   */
  async getMeetings(
    options: {
      page?: number
      limit?: number
      startDate?: string
      endDate?: string
      partnerType?: PartnerType
      meetStatus?: string
      companyName?: string
    } = {}
  ): Promise<{
    meetings: IMeetingDocument[]
    total: number
    page: number
    limit: number
    totalPages: number
  }> {
    try {
      return await this.meetingRepository.getMeetings(options)
    } catch (error) {
      throw new MeetingServiceError("Failed to get meetings from database", error as Error)
    }
  }

  /**
   * Get meeting by ID from database (primary method)
   */
  async getMeetingById(meetId: number): Promise<IMeetingDocument | null> {
    try {
      return await this.meetingRepository.getMeetingById(meetId)
    } catch (error) {
      throw new MeetingServiceError(`Failed to get meeting by ID: ${meetId}`, error as Error)
    }
  }

  /**
   * Get meeting statistics from database
   */
  async getMeetingStatistics(): Promise<any> {
    try {
      return await (Meeting as any).getStatistics()
    } catch (error) {
      throw new MeetingServiceError("Failed to get meeting statistics", error as Error)
    }
  }
}
