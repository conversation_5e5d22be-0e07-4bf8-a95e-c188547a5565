/**
 * TypeScript interfaces for meeting data and CVM API responses
 */

import type { CVMMeetingInfoResponse, CVMMOMInfoResponse } from "@/schemas/cvm-api"
import type { ProcessedMeetingData } from "@/schemas/meeting"

// Enums and Constants
export enum PartnerType {
  EXISTING_GROUP = "Existing Group",
  NEW_PARTNER_GROUP = "New Partner Group",
  NEW_CUSTOMER_GROUP = "New Customer Group",
  UNKNOWN = "",
}

export const EXISTING_GROUP_BLOCKS = [
  "Block AB1",
  "Block AB2",
  "Block AB3",
  "Block AB4",
  "Block AB5",
  "Block AB6",
  "Block ABP",
  "Block Retention",
  "Tier 1",
  "Tier 2",
  "Tier 3",
  "Block New Customer",
  "Block ABC1 (Channel)",
  "Block ABC2 (Channel)",
  "Block ABC3 (Channel)",
  "Block Retention SI",
  "Block New Partner 1 (Channel)",
  "Block New Partner 2 (Channel)",
]

export const NEW_PARTNER_BUSINESSES = ["SI", "Software", "Implementer"]
export const NEW_CUSTOMER_BUSINESSES = ["End user"]

export const PRIVILEGE_MAPPING = {
  PLC: "PLC",
  CLC: "CLC",
  "Update Service": "",
  "เหตุผลอื่น ๆ": "",
} as const
