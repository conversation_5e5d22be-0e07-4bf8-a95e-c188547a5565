import type { ColumnDef } from "@tanstack/react-table"

import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"

import type { MeetingDocument } from "@/features/platform/schemas/meeting"

import { DataTableColumnHeader } from "./data-table-column-header"
import { DataTableRowActions } from "./data-table-row-actions"

export const columns: ColumnDef<MeetingDocument>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "meetDate",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Meet Date" />,
  },
  {
    accessorKey: "meetTime",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Meet Time" />,
  },
  {
    accessorKey: "companyName",
    header: ({ column }) => "Company Name",
  },
  {
    accessorKey: "meetingChannel",
    header: ({ column }) => "Meeting Channel",
  },
  {
    accessorKey: "customerConference",
    header: ({ column }) => "Customer Conference",
  },
  {
    accessorKey: "customerOtherDesc",
    header: ({ column }) => "Customer Other Description",
  },
  {
    accessorKey: "block",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Block" />,
  },
  {
    accessorKey: "sumGrade",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Sum Grade" />,
  },
  {
    accessorKey: "saleName",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Sale Name" />,
  },
  {
    accessorKey: "blockTeamSale",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Block Team Sale" />,
  },
  {
    accessorKey: "presaleVisitName",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Presale Visit Name" />,
  },
  {
    accessorKey: "typeOfBusiness",
    header: ({ column }) => "Type of Business",
  },
  {
    accessorKey: "partnerType",
    header: ({ column }) => "Partner Type",
  },
  {
    accessorKey: "privateCustomerTransformBusinesses",
    header: ({ column }) => "หัวบิล",
  },
  {
    accessorKey: "customerVisit",
    header: ({ column }) => "Customer Visit",
  },
  {
    accessorKey: "privilege",
    header: ({ column }) => "Privilege",
  },
  {
    accessorKey: "presalesPrivilegeName",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Presales Privilege Name" />,
  },
  {
    accessorKey: "customerName",
    header: ({ column }) => "Customer Name",
  },
  {
    accessorKey: "customerPosition",
    header: ({ column }) => "Customer Position",
  },
  {
    accessorKey: "customerPhone",
    header: ({ column }) => "Customer Phone",
  },
  {
    accessorKey: "customerEmail",
    header: ({ column }) => "Customer Email",
  },
  {
    accessorKey: "customerAddressCompany",
    header: ({ column }) => "Customer Address Company",
  },
  {
    accessorKey: "taxId",
    header: ({ column }) => "Tax ID",
  },
  {
    accessorKey: "meetErpCode",
    header: ({ column }) => "ERP Code",
  },
  {
    accessorKey: "meetStatus",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Meet Status" />,
  },
  {
    accessorKey: "statusMeeting",
    header: ({ column }) => "Status Meeting",
  },
  {
    accessorKey: "comment",
    header: ({ column }) => "Comments",
  },
  {
    accessorKey: "saleMOM",
    header: ({ column }) => "Sale MOM",
  },
  {
    accessorKey: "presaleMOM",
    header: ({ column }) => "Presales MOM",
  },
  {
    accessorKey: "service",
    header: ({ column }) => "Service",
  },
  {
    accessorKey: "statusService",
    header: ({ column }) => "Status Service",
  },
  {
    accessorKey: "partnerERP",
    header: ({ column }) => "Partner ERP",
  },
  {
    accessorKey: "serviceERP",
    header: ({ column }) => "Service ERP",
  },
  {
    accessorKey: "partnerCRM",
    header: ({ column }) => "Partner CRM",
  },
  {
    accessorKey: "serviceCRM",
    header: ({ column }) => "Service CRM",
  },
  {
    accessorKey: "partnerHRM",
    header: ({ column }) => "Partner HRM",
  },
  {
    accessorKey: "serviceHRM",
    header: ({ column }) => "Service HRM",
  },
  {
    accessorKey: "partnerSupplyChain",
    header: ({ column }) => "Partner Supply Chain",
  },
  {
    accessorKey: "serviceSupplyChain",
    header: ({ column }) => "Service Supply Chain",
  },
  {
    accessorKey: "partnerWorkflow",
    header: ({ column }) => "Partner Workflow",
  },
  {
    accessorKey: "serviceWorkflow",
    header: ({ column }) => "Service Workflow",
  },
  {
    accessorKey: "partnerEcommerceWeb",
    header: ({ column }) => "Partner E-commerce/Web",
  },
  {
    accessorKey: "serviceEcommerceWeb",
    header: ({ column }) => "Service E-commerce/Web",
  },
  {
    accessorKey: "meetingCreatedAt",
    header: ({ column }) => "Meeting Created At",
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
]
