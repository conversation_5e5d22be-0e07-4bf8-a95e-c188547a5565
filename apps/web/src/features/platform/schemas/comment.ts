/**
 * Zod schemas for processed meeting data validation
 */

import { z } from "zod"

// User Comment Schemas
export const addCommentSchema = z.object({
  meetId: z.number(),
  comment: z.string().min(1).max(1000),
  userId: z.string(),
})

export const getCommentsSchema = z.object({
  commentId: z.number(),
})

export const deleteCommentSchema = z.object({
  commentId: z.string(),
})

// Meeting Comment Schema
export const meetingCommentSchema = z.object({
  _id: z.string().optional(),
  userId: z.string(),
  comment: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export type AddComment = z.infer<typeof addCommentSchema>
export type GetComments = z.infer<typeof getCommentsSchema>
export type DeleteComment = z.infer<typeof deleteCommentSchema>
export type MeetingComment = z.infer<typeof meetingCommentSchema>
