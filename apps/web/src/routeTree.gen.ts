/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as SidebarRouteRouteImport } from './routes/_sidebar.route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as SidebarWarroomIndexRouteImport } from './routes/_sidebar.warroom.index'
import { Route as SidebarSyncIndexRouteImport } from './routes/_sidebar.sync.index'
import { Route as SidebarPlatformIndexRouteImport } from './routes/_sidebar.platform.index'
import { Route as SidebarDashboardIndexRouteImport } from './routes/_sidebar.dashboard.index'

const SidebarRouteRoute = SidebarRouteRouteImport.update({
  id: '/_sidebar',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const SidebarWarroomIndexRoute = SidebarWarroomIndexRouteImport.update({
  id: '/warroom/',
  path: '/warroom/',
  getParentRoute: () => SidebarRouteRoute,
} as any)
const SidebarSyncIndexRoute = SidebarSyncIndexRouteImport.update({
  id: '/sync/',
  path: '/sync/',
  getParentRoute: () => SidebarRouteRoute,
} as any)
const SidebarPlatformIndexRoute = SidebarPlatformIndexRouteImport.update({
  id: '/platform/',
  path: '/platform/',
  getParentRoute: () => SidebarRouteRoute,
} as any)
const SidebarDashboardIndexRoute = SidebarDashboardIndexRouteImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => SidebarRouteRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/dashboard': typeof SidebarDashboardIndexRoute
  '/platform': typeof SidebarPlatformIndexRoute
  '/sync': typeof SidebarSyncIndexRoute
  '/warroom': typeof SidebarWarroomIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/dashboard': typeof SidebarDashboardIndexRoute
  '/platform': typeof SidebarPlatformIndexRoute
  '/sync': typeof SidebarSyncIndexRoute
  '/warroom': typeof SidebarWarroomIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_sidebar': typeof SidebarRouteRouteWithChildren
  '/_sidebar/dashboard/': typeof SidebarDashboardIndexRoute
  '/_sidebar/platform/': typeof SidebarPlatformIndexRoute
  '/_sidebar/sync/': typeof SidebarSyncIndexRoute
  '/_sidebar/warroom/': typeof SidebarWarroomIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/dashboard' | '/platform' | '/sync' | '/warroom'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/dashboard' | '/platform' | '/sync' | '/warroom'
  id:
    | '__root__'
    | '/'
    | '/_sidebar'
    | '/_sidebar/dashboard/'
    | '/_sidebar/platform/'
    | '/_sidebar/sync/'
    | '/_sidebar/warroom/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  SidebarRouteRoute: typeof SidebarRouteRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_sidebar': {
      id: '/_sidebar'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof SidebarRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_sidebar/warroom/': {
      id: '/_sidebar/warroom/'
      path: '/warroom'
      fullPath: '/warroom'
      preLoaderRoute: typeof SidebarWarroomIndexRouteImport
      parentRoute: typeof SidebarRouteRoute
    }
    '/_sidebar/sync/': {
      id: '/_sidebar/sync/'
      path: '/sync'
      fullPath: '/sync'
      preLoaderRoute: typeof SidebarSyncIndexRouteImport
      parentRoute: typeof SidebarRouteRoute
    }
    '/_sidebar/platform/': {
      id: '/_sidebar/platform/'
      path: '/platform'
      fullPath: '/platform'
      preLoaderRoute: typeof SidebarPlatformIndexRouteImport
      parentRoute: typeof SidebarRouteRoute
    }
    '/_sidebar/dashboard/': {
      id: '/_sidebar/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof SidebarDashboardIndexRouteImport
      parentRoute: typeof SidebarRouteRoute
    }
  }
}

interface SidebarRouteRouteChildren {
  SidebarDashboardIndexRoute: typeof SidebarDashboardIndexRoute
  SidebarPlatformIndexRoute: typeof SidebarPlatformIndexRoute
  SidebarSyncIndexRoute: typeof SidebarSyncIndexRoute
  SidebarWarroomIndexRoute: typeof SidebarWarroomIndexRoute
}

const SidebarRouteRouteChildren: SidebarRouteRouteChildren = {
  SidebarDashboardIndexRoute: SidebarDashboardIndexRoute,
  SidebarPlatformIndexRoute: SidebarPlatformIndexRoute,
  SidebarSyncIndexRoute: SidebarSyncIndexRoute,
  SidebarWarroomIndexRoute: SidebarWarroomIndexRoute,
}

const SidebarRouteRouteWithChildren = SidebarRouteRoute._addFileChildren(
  SidebarRouteRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  SidebarRouteRoute: SidebarRouteRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
