import { useQuery } from "@tanstack/react-query"
import { createFileRoute } from "@tanstack/react-router"
import { zodValidator } from "@tanstack/zod-adapter"
import { Download } from "lucide-react"
import { z } from "zod"

import { trpc } from "@/utils/trpc"
import { Button } from "@/components/ui/button"

import { columns } from "@/features/platform/components/columns"
import { DataTable } from "@/features/platform/components/data-table"

const searchSchema = z.object({
  startDate: z.string().nullable().optional(),
  endDate: z.string().nullable().optional(),
  page: z.number().default(1),
  limit: z.number().default(10),
})

export const Route = createFileRoute("/_sidebar/platform/")({
  validateSearch: zodValidator(searchSchema),
  component: RouteComponent,
})

function RouteComponent() {
  const search = Route.useSearch()

  const { data, isPending } = useQuery(
    trpc.meeting.getMeetings.queryOptions({
      startDate: search.startDate || "2024-01-01",
      endDate: search.endDate || "2025-12-31",
      page: search.page,
      limit: search.limit,
    })
  )

  if (isPending) {
    return <div>Loading...</div>
  }

  return (
    <div className="flex flex-col space-y-4 py-4">
      <div className="flex justify-between px-4 lg:px-6">
        <h1 className="text-3xl font-bold">Platform Meeting</h1>

        <Button>
          <Download className="size-4" />
          <span>Export</span>
        </Button>
      </div>

      <DataTable columns={columns} data={data?.data} limit={search.limit} />
    </div>
  )
}
