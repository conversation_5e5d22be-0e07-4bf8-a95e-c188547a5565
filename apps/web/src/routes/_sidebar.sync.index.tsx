import { useMutation } from "@tanstack/react-query"
import { createFileRoute } from "@tanstack/react-router"
import { Loader2 } from "lucide-react"
import { toast } from "sonner"

import { trpc } from "@/utils/trpc"
import { Button } from "@/components/ui/button"

export const Route = createFileRoute("/_sidebar/sync/")({
  component: RouteComponent,
})

function RouteComponent() {
  const syncWithOutSaveMutation = useMutation(
    trpc.sync.fetchAndProcess.mutationOptions({
      onError: (error) => {
        console.error(error)
      },
    })
  )

  const syncWithSaveMutation = useMutation(
    trpc.sync.fetchAndSave.mutationOptions({
      onError: (error) => {
        console.error(error)
      },
    })
  )

  function handleSyncWithOutSave() {
    toast.promise(
      syncWithOutSaveMutation.mutateAsync({
        startDate: "2024-01-01",
        endDate: "2025-12-31",
        startTime: "00:00:00",
        endTime: "23:59:59",
      }),
      {
        loading: "Syncing...",
        success: "Synced",
        error: "Error",
      }
    )
  }

  function handleSyncWithSave() {
    toast.promise(
      syncWithSaveMutation.mutateAsync({
        startDate: "2024-01-01",
        endDate: "2025-12-31",
        startTime: "00:00:00",
        endTime: "23:59:59",
      }),
      {
        loading: "Syncing...",
        success: "Synced",
        error: "Error",
      }
    )
  }

  return (
    <div className="flex flex-col space-y-4 px-4 py-4 lg:px-6">
      <div className="mx-auto flex min-h-svh items-center justify-center">
        <div className="flex flex-1 gap-4">
          <Button onClick={handleSyncWithOutSave}>
            {syncWithOutSaveMutation.isPending ? (
              <>
                <Loader2 className="size-4 animate-spin" />
                <span>Loading...</span>
              </>
            ) : (
              <span>Sync without save</span>
            )}
          </Button>

          <Button onClick={handleSyncWithSave}>
            {syncWithSaveMutation.isPending ? (
              <>
                <Loader2 className="size-4 animate-spin" />
                <span>Loading...</span>
              </>
            ) : (
              <span>Sync with save</span>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
